#!/usr/bin/env python3
"""
Script to seed the products table with sample discount data
"""

from sqlalchemy.orm import Session
from database import SessionLocal, Product, create_tables
from datetime import datetime, timedelta
import random

def create_sample_products():
    """Create sample products with discounts"""
    
    # Sample products data based on the image
    sample_products = [
        {
            "name": "<PERSON><PERSON><PERSON> (Rice)",
            "store": "HyperMaxi",
            "original_price": 201.75,
            "discount_price": 172.34,
        },
        {
            "name": "Cereal",
            "store": "Fidalga",
            "original_price": 201.75,
            "discount_price": 172.34,
        },
        {
            "name": "Manzana",
            "store": "HiperMaxi",
            "original_price": 201.75,
            "discount_price": 172.34,
        },
        {
            "name": "Lavadora",
            "store": "Fidalga",
            "original_price": 3000.0,
            "discount_price": 2990.0,
        },
        {
            "name": "Tostadora",
            "store": "HiperMaxi",
            "original_price": 1200.0,
            "discount_price": 1100.0,
        },
        # Additional sample products
        {
            "name": "<PERSON>che",
            "store": "SuperMarket",
            "original_price": 85.50,
            "discount_price": 75.00,
        },
        {
            "name": "Pan",
            "store": "Panadería Central",
            "original_price": 45.00,
            "discount_price": 40.00,
        },
        {
            "name": "Pollo",
            "store": "Carnicería López",
            "original_price": 180.00,
            "discount_price": 160.00,
        },
        {
            "name": "Televisor 55\"",
            "store": "ElectroMax",
            "original_price": 15000.0,
            "discount_price": 13500.0,
        },
        {
            "name": "Refrigerador",
            "store": "HyperMaxi",
            "original_price": 25000.0,
            "discount_price": 22000.0,
        }
    ]
    
    db = SessionLocal()
    try:
        # Clear existing products (optional)
        print("Clearing existing products...")
        db.query(Product).delete()
        
        print("Creating sample products...")
        for product_data in sample_products:
            # Calculate discount percentage
            discount_percentage = None
            if product_data["discount_price"]:
                discount_percentage = ((product_data["original_price"] - product_data["discount_price"]) / product_data["original_price"]) * 100
            
            # Create random last_update times (within last 7 days)
            last_update = datetime.utcnow() - timedelta(days=random.randint(0, 7), hours=random.randint(0, 23))
            
            product = Product(
                name=product_data["name"],
                store=product_data["store"],
                original_price=product_data["original_price"],
                discount_price=product_data["discount_price"],
                discount_percentage=discount_percentage,
                last_update=last_update
            )
            
            db.add(product)
            print(f"Added: {product.name} at {product.store} - ${product.original_price} -> ${product.discount_price}")
        
        db.commit()
        print(f"\n✅ Successfully created {len(sample_products)} sample products!")
        
    except Exception as e:
        print(f"❌ Error creating sample products: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🌱 Seeding products database...")
    
    # Ensure tables exist
    try:
        create_tables()
    except Exception as e:
        print(f"⚠️  Database connection issue: {e}")
        print("Make sure MySQL is running and database 'carlos' exists")
        exit(1)
    
    create_sample_products()
    print("🎉 Database seeding completed!")
