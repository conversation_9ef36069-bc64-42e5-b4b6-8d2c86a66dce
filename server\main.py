from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status, Query
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from sqlalchemy import or_
from datetime import timedelta
import os
from dotenv import load_dotenv
from typing import List, Optional

from database import create_tables, get_db, Product, UserFavorite
from schemas import (
    UserCreate, UserLogin, UserResponse, Token, GoogleUserCreate,
    ProductCreate, ProductResponse, FavoriteCreate, FavoriteResponse, FavoriteToggleResponse
)
from auth import (
    authenticate_user,
    create_user,
    create_google_user,
    create_access_token,
    get_current_active_user,
    get_user_by_email,
    get_user_by_google_id,
    verify_google_token,
    ACCESS_TOKEN_EXPIRE_MINUTES
)

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(title="StoreDiscount API", version="1.0.0")

# CORS middleware for React Native app
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your React Native app's origin
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create database tables on startup
@app.on_event("startup")
async def startup_event():
    try:
        create_tables()
    except Exception as e:
        print(f"⚠️  Database connection failed: {e}")
        print("🔧 Server will start but database features won't work until MySQL is configured")
        print("📋 To fix this:")
        print("   1. Start MySQL server")
        print("   2. Create database 'carlos'")
        print("   3. Run: uv run python init_db.py")
        print("   4. Restart the server")

# Routes


@app.get("/")
async def root():
    return {"message": "StoreDiscount API is running"}

@app.post("/auth/register", response_model=Token)
async def register(user: UserCreate, db: Session = Depends(get_db)):
    # Check if user already exists
    db_user = get_user_by_email(db, email=user.email)
    if db_user:
        raise HTTPException(
            status_code=400,
            detail="Email already registered"
        )

    # Create new user
    db_user = create_user(db=db, email=user.email, password=user.password)

    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.post("/auth/register-or-login", response_model=Token)
async def register_or_login(user: UserCreate, db: Session = Depends(get_db)):
    """
    Register a new user or login if user already exists.
    If the email is already registered, attempt to login with the provided password.
    If the email is not registered, create a new account.
    """
    # Check if user already exists
    db_user = get_user_by_email(db, email=user.email)

    if db_user:
        # User exists, attempt to login
        authenticated_user = authenticate_user(db, user.email, user.password)
        if not authenticated_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect password for existing account",
                headers={"WWW-Authenticate": "Bearer"},
            )
        db_user = authenticated_user
    else:
        # User doesn't exist, create new account
        db_user = create_user(db=db, email=user.email, password=user.password)

    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": db_user.email}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.post("/auth/login", response_model=Token)
async def login(user: UserLogin, db: Session = Depends(get_db)):
    authenticated_user = authenticate_user(db, user.email, user.password)
    if not authenticated_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": authenticated_user.email}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/auth/me", response_model=UserResponse)
async def read_users_me(current_user = Depends(get_current_active_user)):
    return current_user

@app.post("/auth/google", response_model=Token)
async def google_auth(google_token: dict, db: Session = Depends(get_db)):
    """Authenticate with Google OAuth token"""
    try:
        # Verify the Google token
        google_user_info = await verify_google_token(google_token.get("token"))

        # Check if user exists by Google ID
        existing_user = get_user_by_google_id(db, google_user_info["google_id"])

        if existing_user:
            # User exists, log them in
            user = existing_user
        else:
            # Check if user exists by email (for account linking)
            existing_email_user = get_user_by_email(db, google_user_info["email"])

            if existing_email_user:
                # Link Google account to existing email account
                existing_email_user.google_id = google_user_info["google_id"]
                existing_email_user.provider = "google"
                existing_email_user.first_name = google_user_info.get("first_name")
                existing_email_user.last_name = google_user_info.get("last_name")
                existing_email_user.profile_picture_url = google_user_info.get("profile_picture_url")
                db.commit()
                db.refresh(existing_email_user)
                user = existing_email_user
            else:
                # Create new user
                google_user_data = GoogleUserCreate(**google_user_info)
                user = create_google_user(db, google_user_data)

        # Create access token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.email}, expires_delta=access_token_expires
        )

        return {"access_token": access_token, "token_type": "bearer"}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Google authentication failed: {str(e)}"
        )

# Product endpoints

@app.get("/products/discounts", response_model=List[ProductResponse])
async def get_discount_products(
    search: Optional[str] = Query(None, description="Search products by name or store"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all products that have discounts, with optional search"""
    query = db.query(Product).filter(Product.discount_price.isnot(None))

    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Product.name.ilike(search_term),
                Product.store.ilike(search_term)
            )
        )

    products = query.order_by(Product.last_update.desc()).all()

    # Get user's favorite product IDs
    user_favorite_ids = set()
    if current_user:
        favorites = db.query(UserFavorite.product_id).filter(
            UserFavorite.user_id == current_user.id
        ).all()
        user_favorite_ids = {fav[0] for fav in favorites}

    # Add is_favorite field to each product
    result = []
    for product in products:
        product_dict = {
            "id": product.id,
            "name": product.name,
            "store": product.store,
            "original_price": product.original_price,
            "discount_price": product.discount_price,
            "discount_percentage": product.discount_percentage,
            "last_update": product.last_update,
            "created_at": product.created_at,
            "is_favorite": product.id in user_favorite_ids
        }
        result.append(ProductResponse(**product_dict))

    return result

@app.post("/products", response_model=ProductResponse)
async def create_product(
    product: ProductCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Create a new product (requires authentication)"""
    # Calculate discount percentage if both prices are provided
    discount_percentage = None
    if product.discount_price and product.original_price > 0:
        discount_percentage = ((product.original_price - product.discount_price) / product.original_price) * 100

    db_product = Product(
        name=product.name,
        store=product.store,
        original_price=product.original_price,
        discount_price=product.discount_price,
        discount_percentage=discount_percentage
    )

    db.add(db_product)
    db.commit()
    db.refresh(db_product)
    return db_product

@app.get("/products", response_model=List[ProductResponse])
async def get_all_products(
    search: Optional[str] = Query(None, description="Search products by name or store"),
    db: Session = Depends(get_db)
):
    """Get all products with optional search"""
    query = db.query(Product)

    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Product.name.ilike(search_term),
                Product.store.ilike(search_term)
            )
        )

    products = query.order_by(Product.last_update.desc()).all()
    return products

# Favorites endpoints

@app.post("/favorites/toggle", response_model=FavoriteToggleResponse)
async def toggle_favorite(
    favorite_data: FavoriteCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Toggle a product as favorite for the current user"""
    # Check if product exists
    product = db.query(Product).filter(Product.id == favorite_data.product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    # Check if already favorited
    existing_favorite = db.query(UserFavorite).filter(
        UserFavorite.user_id == current_user.id,
        UserFavorite.product_id == favorite_data.product_id
    ).first()

    if existing_favorite:
        # Remove from favorites
        db.delete(existing_favorite)
        db.commit()
        return FavoriteToggleResponse(is_favorite=False, message="Removed from favorites")
    else:
        # Check if user has reached the limit of 20 favorites
        user_favorites_count = db.query(UserFavorite).filter(
            UserFavorite.user_id == current_user.id
        ).count()

        if user_favorites_count >= 20:
            # Remove the oldest favorite (FIFO)
            oldest_favorite = db.query(UserFavorite).filter(
                UserFavorite.user_id == current_user.id
            ).order_by(UserFavorite.created_at.asc()).first()

            if oldest_favorite:
                db.delete(oldest_favorite)

        # Add to favorites
        new_favorite = UserFavorite(
            user_id=current_user.id,
            product_id=favorite_data.product_id
        )
        db.add(new_favorite)
        db.commit()
        return FavoriteToggleResponse(is_favorite=True, message="Added to favorites")

@app.get("/favorites", response_model=List[FavoriteResponse])
async def get_user_favorites(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all favorites for the current user"""
    favorites = db.query(UserFavorite).filter(
        UserFavorite.user_id == current_user.id
    ).order_by(UserFavorite.created_at.desc()).all()

    return favorites

@app.get("/favorites/check/{product_id}")
async def check_favorite_status(
    product_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Check if a product is favorited by the current user"""
    favorite = db.query(UserFavorite).filter(
        UserFavorite.user_id == current_user.id,
        UserFavorite.product_id == product_id
    ).first()

    return {"is_favorite": favorite is not None}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
