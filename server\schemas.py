from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import Optional

# User schemas
class UserBase(BaseModel):
    email: EmailStr

class UserCreate(UserBase):
    password: str

class UserLogin(UserBase):
    password: str

class GoogleUserCreate(BaseModel):
    email: EmailStr
    google_id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    profile_picture_url: Optional[str] = None

class UserResponse(UserBase):
    id: int
    is_active: bool
    provider: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    profile_picture_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class UserInDB(UserResponse):
    hashed_password: Optional[str] = None
    google_id: Optional[str] = None

# Token schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    email: Optional[str] = None

# Product schemas
class ProductBase(BaseModel):
    name: str
    store: str
    original_price: float
    discount_price: Optional[float] = None
    discount_percentage: Optional[float] = None

class ProductCreate(ProductBase):
    pass

class ProductResponse(ProductBase):
    id: int
    last_update: datetime
    created_at: datetime
    is_favorite: Optional[bool] = False  # Will be set based on user's favorites

    class Config:
        from_attributes = True

# User Favorites schemas
class FavoriteCreate(BaseModel):
    product_id: int

class FavoriteResponse(BaseModel):
    id: int
    user_id: int
    product_id: int
    created_at: datetime
    product: ProductResponse

    class Config:
        from_attributes = True

class FavoriteToggleResponse(BaseModel):
    is_favorite: bool
    message: str
