#!/usr/bin/env python3
"""
<PERSON><PERSON>t to add more products to test the 20-item favorites limit
"""

from sqlalchemy.orm import Session
from database import SessionLocal, Product
from datetime import datetime, timedelta
import random

def add_more_products():
    """Add more products to test the favorites limit"""
    
    # Additional sample products
    additional_products = [
        {"name": "Yogurt", "store": "SuperMarket", "original_price": 65.00, "discount_price": 55.00},
        {"name": "Queso", "store": "Lácteos Central", "original_price": 120.00, "discount_price": 100.00},
        {"name": "Jamón", "store": "Carnicería López", "original_price": 250.00, "discount_price": 220.00},
        {"name": "Pasta", "store": "Fidalga", "original_price": 35.00, "discount_price": 30.00},
        {"name": "Aceite", "store": "HyperMaxi", "original_price": 80.00, "discount_price": 70.00},
        {"name": "<PERSON><PERSON><PERSON><PERSON>", "store": "SuperMarket", "original_price": 45.00, "discount_price": 40.00},
        {"name": "Café", "store": "Café Express", "original_price": 150.00, "discount_price": 130.00},
        {"name": "Té", "store": "Café Express", "original_price": 90.00, "discount_price": 75.00},
        {"name": "Galletas", "store": "Panadería Central", "original_price": 55.00, "discount_price": 45.00},
        {"name": "Chocolate", "store": "Dulcería Moderna", "original_price": 85.00, "discount_price": 70.00},
        {"name": "Shampoo", "store": "Farmacia Plus", "original_price": 120.00, "discount_price": 100.00},
        {"name": "Jabón", "store": "Farmacia Plus", "original_price": 25.00, "discount_price": 20.00},
        {"name": "Detergente", "store": "HyperMaxi", "original_price": 95.00, "discount_price": 80.00},
        {"name": "Papel Higiénico", "store": "SuperMarket", "original_price": 60.00, "discount_price": 50.00},
        {"name": "Toallas", "store": "Hogar Total", "original_price": 180.00, "discount_price": 150.00},
        {"name": "Microondas", "store": "ElectroMax", "original_price": 8000.00, "discount_price": 7200.00},
        {"name": "Licuadora", "store": "ElectroMax", "original_price": 2500.00, "discount_price": 2200.00},
        {"name": "Plancha", "store": "Hogar Total", "original_price": 1800.00, "discount_price": 1600.00},
        {"name": "Aspiradora", "store": "ElectroMax", "original_price": 12000.00, "discount_price": 10500.00},
        {"name": "Ventilador", "store": "Hogar Total", "original_price": 3500.00, "discount_price": 3000.00},
    ]
    
    db = SessionLocal()
    try:
        print("Adding more products for testing...")
        
        for product_data in additional_products:
            # Calculate discount percentage
            discount_percentage = None
            if product_data["discount_price"]:
                discount_percentage = ((product_data["original_price"] - product_data["discount_price"]) / product_data["original_price"]) * 100
            
            # Create random last_update times (within last 7 days)
            last_update = datetime.utcnow() - timedelta(days=random.randint(0, 7), hours=random.randint(0, 23))
            
            product = Product(
                name=product_data["name"],
                store=product_data["store"],
                original_price=product_data["original_price"],
                discount_price=product_data["discount_price"],
                discount_percentage=discount_percentage,
                last_update=last_update
            )
            
            db.add(product)
            print(f"Added: {product.name} at {product.store} - ${product.original_price} -> ${product.discount_price}")
        
        db.commit()
        print(f"\n✅ Successfully added {len(additional_products)} additional products!")
        
        # Check total count
        total_products = db.query(Product).count()
        print(f"Total products in database: {total_products}")
        
    except Exception as e:
        print(f"❌ Error adding products: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🛒 Adding more products to test favorites limit...")
    add_more_products()
    print("🎉 Products added successfully!")
